# Product Overview

## BMad Pydantic AI Agents

A comprehensive multi-agent system implementing the BMad Method (Breakthrough Method of Agile AI-driven Development) using the Pydantic AI framework. The system transforms traditional software development by providing specialized AI agents for each role in the development process.

### Core Purpose

- **Primary Function**: Multi-agent AI system for software development workflows
- **Target Users**: Development teams, project managers, architects, and individual developers
- **Key Value**: Automates and enhances software development processes through specialized AI agents

### Key Features

- **Specialized Agents**: 9 role-specific agents (Analyst, Architect, PM, PO, SM, Developer, QA, UX Expert, DevOps)
- **Orchestration**: Central BMad Orchestrator for coordinating multi-agent workflows
- **Workflow Automation**: Pre-built workflows for brownfield and greenfield development
- **Backward Compatibility**: Maintains compatibility with existing Pydantic AI examples
- **Multi-Model Support**: Works with Google Gemini, OpenAI, Anthropic, and other AI providers

### Development Approach

- **Methodology**: BMad Method - AI-driven agile development
- **Architecture**: Microservices-style agent architecture with centralized orchestration
- **Integration**: Extends existing Pydantic AI framework without breaking changes
- **Scalability**: Designed for both individual agent usage and complex multi-agent workflows

### Current State

- **Status**: Active development with working base infrastructure
- **Version**: 1.0.0 in development
- **Maturity**: Production-ready base with expanding agent capabilities
- **Documentation**: Comprehensive PRD, architecture docs, and user guides available