{"workflow_id": "80e9d900-e758-403b-8478-d9bef26a107c", "workflow_type": "brownfield-fullstack", "current_step": "scope_classification", "status": "active", "progress": 0.0, "started_at": "2025-08-03 16:17:55.833234", "updated_at": "2025-08-03 16:17:55.833234", "context": {"enhancement_description": "test enhancement", "project_context": {"test": "data"}}, "participants": [], "completed_steps": [], "pending_steps": ["documentation_check", "project_analysis", "prd_creation", "architecture_decisions", "story_creation"], "agent_contexts": {}, "shared_context": {"enhancement_description": "test enhancement", "project_context": {"test": "data"}}}