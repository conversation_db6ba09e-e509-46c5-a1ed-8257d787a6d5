/* BMad Agents UI Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    margin: 10px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.sidebar {
    width: 300px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px;
    border-radius: 15px 0 0 15px;
    overflow-y: auto;
}

.sidebar h2 {
    margin-bottom: 20px;
    font-size: 1.5em;
    text-align: center;
    color: #ecf0f1;
}

.agent-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    list-style: none;
}

.agent-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.agent-item.active {
    background: rgba(52, 152, 219, 0.3);
    border-color: #3498db;
}

.agent-icon {
    font-size: 1.5em;
    margin-bottom: 8px;
    text-align: center;
}

.agent-name {
    font-size: 1.1em;
    font-weight: bold;
    margin-bottom: 5px;
    color: #ecf0f1;
}

.agent-description {
    font-size: 0.9em;
    color: #bdc3c7;
    line-height: 1.4;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.header {
    background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
    color: white;
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

.header h1 {
    font-size: 1.8em;
    margin-bottom: 5px;
}

.header p {
    opacity: 0.9;
    font-size: 1em;
}

.messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    padding: 12px 16px;
    border-radius: 12px;
    max-width: 80%;
    word-wrap: break-word;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.user {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.message.agent {
    background: white;
    border: 1px solid #e1e8ed;
    margin-right: auto;
    border-bottom-left-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message.system {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    margin: 0 auto;
    text-align: center;
    font-style: italic;
}

.message-header {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 0.9em;
    opacity: 0.8;
}

.chat-input {
    display: flex;
    padding: 20px;
    background: white;
    border-top: 1px solid #ddd;
    gap: 10px;
}

.chat-input input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 25px;
    font-size: 1em;
    outline: none;
    transition: border-color 0.3s ease;
}

.chat-input input:focus {
    border-color: #3498db;
}

.chat-input button {
    padding: 12px 24px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: all 0.3s ease;
}

.chat-input button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.chat-input button:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.workflow-panel {
    width: 250px;
    background: #f8f9fa;
    border-left: 1px solid #ddd;
    padding: 20px;
    border-radius: 0 15px 15px 0;
    overflow-y: auto;
}

.workflow-panel h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 1.2em;
}

.workflow-item {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.workflow-item:hover {
    border-color: #3498db;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.workflow-item h4 {
    font-size: 1em;
    margin-bottom: 5px;
    color: #2c3e50;
}

.workflow-item p {
    font-size: 0.85em;
    color: #7f8c8d;
    line-height: 1.3;
}

.typing-indicator {
    display: none;
    padding: 10px 16px;
    background: #f1f3f4;
    border-radius: 12px;
    margin-bottom: 15px;
    max-width: 80px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

.typing-indicator::after {
    content: '...';
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}

.status-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 20px;
    color: white;
    font-size: 0.9em;
    font-weight: 600;
    z-index: 1000;
    transition: all 0.3s ease;
}

.status-indicator.connected {
    background: #27ae60;
}

.status-indicator.disconnected {
    background: #e74c3c;
}

.status-indicator.connecting {
    background: #f39c12;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        margin: 5px;
    }
    
    .sidebar {
        width: 100%;
        height: 200px;
        border-radius: 15px 15px 0 0;
    }
    
    .workflow-panel {
        width: 100%;
        height: 150px;
        border-radius: 0 0 15px 15px;
        border-left: none;
        border-top: 1px solid #ddd;
    }
    
    .message {
        max-width: 95%;
    }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}