from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import json
import asyncio
import uuid
from datetime import datetime
import sys
import os

# Add the parent directory to the path to import bmad_agents
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from bmad_agents.agents.orchestrator import BMadOrchestrator
from bmad_agents.base.models import AgentRequest, AgentResponse
from bmad_agents.base.config import config
from bmad_agents.base.logging_config import setup_logging

# Initialize logging
setup_logging()

app = FastAPI(
    title="BMad Agents Web Interface",
    description="Web interface for interacting with BMad Method AI agents",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
static_dir = os.path.join(os.path.dirname(__file__), '..', 'static')
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Global orchestrator instance
orchestrator = BMadOrchestrator()

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket, session_id: str):
        await websocket.accept()
        self.active_connections[session_id] = websocket
        self.user_sessions[session_id] = {
            "connected_at": datetime.now(),
            "current_agent": None,
            "conversation_history": [],
            "workflow_state": None
        }
    
    def disconnect(self, session_id: str):
        if session_id in self.active_connections:
            del self.active_connections[session_id]
        if session_id in self.user_sessions:
            del self.user_sessions[session_id]
    
    async def send_message(self, session_id: str, message: dict):
        if session_id in self.active_connections:
            await self.active_connections[session_id].send_text(json.dumps(message))
    
    async def broadcast(self, message: dict):
        for session_id in self.active_connections:
            await self.send_message(session_id, message)

manager = ConnectionManager()

# Pydantic models for API
class ChatMessage(BaseModel):
    message: str
    agent: Optional[str] = None
    session_id: str

class AgentInfo(BaseModel):
    name: str
    role: str
    description: str
    capabilities: List[str]
    icon: str

class WorkflowInfo(BaseModel):
    name: str
    description: str
    steps: List[str]
    estimated_duration: str

# API Routes
@app.get("/")
async def read_root():
    """Serve the main web interface."""
    html_path = os.path.join(os.path.dirname(__file__), '..', 'frontend', 'index.html')
    with open(html_path, "r") as f:
        return HTMLResponse(content=f.read())

@app.get("/api/agents", response_model=List[AgentInfo])
async def get_agents():
    """Get list of available agents."""
    agents_info = [
        {
            "name": "analyst",
            "role": "Business Analyst",
            "description": "Analyzes requirements, creates user stories, and identifies business needs",
            "capabilities": ["Requirements Analysis", "User Story Creation", "Risk Assessment"],
            "icon": "📊"
        },
        {
            "name": "architect",
            "role": "Software Architect",
            "description": "Designs system architecture and makes technical decisions",
            "capabilities": ["System Design", "Technology Selection", "Architecture Review"],
            "icon": "🏗️"
        },
        {
            "name": "pm",
            "role": "Project Manager",
            "description": "Manages project timeline, resources, and deliverables",
            "capabilities": ["Project Planning", "Resource Management", "Timeline Creation"],
            "icon": "📋"
        },
        {
            "name": "po",
            "role": "Product Owner",
            "description": "Defines product vision and manages backlog",
            "capabilities": ["Product Vision", "Backlog Management", "Stakeholder Requirements"],
            "icon": "🎯"
        },
        {
            "name": "sm",
            "role": "Scrum Master",
            "description": "Facilitates agile processes and team coordination",
            "capabilities": ["Process Facilitation", "Team Coordination", "Sprint Planning"],
            "icon": "🤝"
        },
        {
            "name": "developer",
            "role": "Developer",
            "description": "Implements code and handles technical execution",
            "capabilities": ["Code Implementation", "Technical Execution", "Code Review"],
            "icon": "💻"
        },
        {
            "name": "qa",
            "role": "Quality Assurance",
            "description": "Ensures quality through testing and bug tracking",
            "capabilities": ["Test Planning", "Quality Assurance", "Bug Tracking"],
            "icon": "🔍"
        },
        {
            "name": "ux",
            "role": "UX Expert",
            "description": "Designs user experience and conducts user research",
            "capabilities": ["User Experience Design", "Usability Testing", "User Research"],
            "icon": "🎨"
        },
        {
            "name": "devops",
            "role": "DevOps Engineer",
            "description": "Manages infrastructure, deployment, and CI/CD",
            "capabilities": ["Infrastructure Management", "Deployment", "CI/CD"],
            "icon": "⚙️"
        },
        {
            "name": "orchestrator",
            "role": "BMad Orchestrator",
            "description": "Coordinates agents and manages workflows",
            "capabilities": ["Agent Coordination", "Workflow Management", "Request Routing"],
            "icon": "🎭"
        }
    ]
    return agents_info

@app.get("/api/workflows", response_model=List[WorkflowInfo])
async def get_workflows():
    """Get list of available workflows."""
    workflows_info = [
        {
            "name": "brownfield-fullstack",
            "description": "Enhance existing full-stack applications",
            "steps": ["Analysis", "Architecture Review", "Implementation Planning", "Development", "Testing"],
            "estimated_duration": "2-4 weeks"
        },
        {
            "name": "greenfield-fullstack",
            "description": "Create new full-stack applications",
            "steps": ["Requirements", "Architecture Design", "Technology Selection", "Development", "Testing", "Deployment"],
            "estimated_duration": "4-8 weeks"
        },
        {
            "name": "brownfield-ui",
            "description": "Enhance existing user interfaces",
            "steps": ["UX Analysis", "Design Review", "Implementation", "Testing"],
            "estimated_duration": "1-2 weeks"
        },
        {
            "name": "greenfield-ui",
            "description": "Create new user interfaces",
            "steps": ["User Research", "Design", "Prototyping", "Implementation", "Testing"],
            "estimated_duration": "2-3 weeks"
        }
    ]
    return workflows_info

@app.post("/api/chat")
async def chat_with_agent(message: ChatMessage):
    """Send a message to an agent or orchestrator."""
    try:
        session_id = message.session_id
        
        # Update session history
        if session_id not in manager.user_sessions:
            manager.user_sessions[session_id] = {
                "connected_at": datetime.now(),
                "current_agent": message.agent,
                "conversation_history": [],
                "workflow_state": None
            }
        
        session = manager.user_sessions[session_id]
        session["conversation_history"].append({
            "type": "user",
            "message": message.message,
            "timestamp": datetime.now().isoformat(),
            "agent": message.agent
        })
        
        # Route to appropriate agent or orchestrator
        if message.agent == "orchestrator" or not message.agent:
            response = await orchestrator.route_request(message.message, {"session_id": session_id})
            response_text = response.message
        else:
            # Execute request with specific agent
            response = await orchestrator.execute_agent_request(message.agent, message.message, {"session_id": session_id})
            response_text = str(response)
        
        # Add response to history
        session["conversation_history"].append({
            "type": "agent",
            "message": response_text,
            "timestamp": datetime.now().isoformat(),
            "agent": message.agent or "orchestrator"
        })
        
        return {
            "response": response_text,
            "agent": message.agent or "orchestrator",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/session/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a session."""
    if session_id in manager.user_sessions:
        return manager.user_sessions[session_id]["conversation_history"]
    return []

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time communication."""
    await manager.connect(websocket, session_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Process the message
            if message_data["type"] == "chat":
                agent = message_data.get("agent")
                message_text = message_data["message"]
                
                # Send typing indicator
                await manager.send_message(session_id, {
                    "type": "typing",
                    "agent": agent or "orchestrator"
                })
                
                try:
                    # Route to appropriate agent
                    if agent == "orchestrator" or not agent:
                        response = await orchestrator.route_request(message_text, {"session_id": session_id})
                        response_text = response.message
                    else:
                        response = await orchestrator.execute_agent_request(agent, message_text, {"session_id": session_id})
                        response_text = str(response)
                    
                    # Send response
                    await manager.send_message(session_id, {
                        "type": "message",
                        "message": response_text,
                        "agent": agent or "orchestrator",
                        "timestamp": datetime.now().isoformat()
                    })
                    
                except Exception as e:
                    await manager.send_message(session_id, {
                        "type": "error",
                        "message": f"Error: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    })
                
    except WebSocketDisconnect:
        manager.disconnect(session_id)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)