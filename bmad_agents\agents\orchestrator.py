from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
from ..base.bmad_agent import BMadAgent
from ..base.communication import AgentRequest, AgentResponse, WorkflowState
from ..base.state_manager import StateManager
from .analyst import AnalystAgent
from .architect import ArchitectAgent
from .pm import PMAgent
from .po import POAgent
from .sm import SMAgent
from .developer import DeveloperAgent
from .qa import QAAgent
from .ux import UXAgent
from .devops import DevOpsAgent

class OrchestrationResponse(BaseModel):
    action: str
    target_agent: Optional[str] = None
    workflow_type: Optional[str] = None
    message: str
    next_steps: List[str] = Field(default_factory=list)
    context_updates: Dict[str, Any] = Field(default_factory=dict)

class AgentSelection(BaseModel):
    selected_agent: str
    rationale: str
    confidence: float = Field(ge=0.0, le=1.0)
    alternative_agents: List[str] = Field(default_factory=list)

class WorkflowRecommendation(BaseModel):
    recommended_workflow: str
    rationale: str
    required_inputs: List[str]
    expected_outputs: List[str]
    estimated_duration: str

class BMadOrchestrator(BMadAgent):
    def __init__(self):
        system_prompt = """
        You are the BMad Orchestrator, the central coordination agent in the BMad Method framework.
        
        Your responsibilities include:
        - Routing user requests to appropriate specialized agents
        - Coordinating multi-agent workflows
        - Managing workflow state and context
        - Providing guidance on BMad Method usage
        - Facilitating smooth handoffs between agents
        
        Available agents and their roles:
        - analyst: Requirements analysis, user story creation, business analysis
        - architect: System design, technical decisions, architecture planning
        - pm: Project planning, resource management, timeline creation
        - po: Product vision, backlog management, stakeholder requirements
        - sm: Process facilitation, team coordination, sprint planning
        - developer: Code implementation, technical execution, development tasks
        - qa: Quality assurance, testing, bug tracking
        - ux: User experience design, usability, user research
        - devops: Infrastructure, deployment, CI/CD, monitoring
        
        Available workflows:
        - brownfield-fullstack: Enhance existing full-stack applications
        - greenfield-fullstack: Create new full-stack applications
        - brownfield-service: Enhance existing backend services
        - greenfield-service: Create new backend services
        - brownfield-ui: Enhance existing user interfaces
        - greenfield-ui: Create new user interfaces
        
        Always provide clear guidance and coordinate effectively between agents.
        Focus on understanding user intent and routing to the most appropriate resource.
        """
        
        super().__init__(
            role="orchestrator",
            system_prompt=system_prompt,
            response_model=OrchestrationResponse
        )
        
        # Initialize agents lazily to avoid circular imports
        self._agents = {}
        self.state_manager = StateManager()
        self.active_workflows: Dict[str, WorkflowState] = {}
    
    def _get_agent(self, agent_name: str):
        """Lazy initialization of agents to avoid circular imports."""
        if agent_name not in self._agents:
            if agent_name == 'analyst':
                self._agents[agent_name] = AnalystAgent()
            elif agent_name == 'architect':
                self._agents[agent_name] = ArchitectAgent()
            elif agent_name == 'pm':
                self._agents[agent_name] = PMAgent()
            elif agent_name == 'po':
                self._agents[agent_name] = POAgent()
            elif agent_name == 'sm':
                self._agents[agent_name] = SMAgent()
            elif agent_name == 'developer':
                self._agents[agent_name] = DeveloperAgent()
            elif agent_name == 'qa':
                self._agents[agent_name] = QAAgent()
            elif agent_name == 'ux':
                self._agents[agent_name] = UXAgent()
            elif agent_name == 'devops':
                self._agents[agent_name] = DevOpsAgent()
            else:
                raise ValueError(f"Unknown agent: {agent_name}")
        
        return self._agents[agent_name]
    
    @property
    def agents(self):
        """Get all available agents."""
        agent_names = ['analyst', 'architect', 'pm', 'po', 'sm', 'developer', 'qa', 'ux', 'devops']
        return {name: self._get_agent(name) for name in agent_names}
    
    async def route_request(self, request: str, context: Dict[str, Any] = None) -> OrchestrationResponse:
        """Route user request to appropriate agent or workflow."""
        prompt = f"""
        Analyze the following user request and determine the appropriate action:

        User Request: {request}

        {f"Current Context: {context}" if context else ""}

        Determine:
        1. What type of request this is (single agent task, workflow initiation, help request, etc.)
        2. Which agent should handle it (if single agent task)
        3. Which workflow should be initiated (if workflow request)
        4. What message to provide to the user
        5. What the next steps should be

        Available actions:
        - "route_to_agent": Route to a specific agent
        - "start_workflow": Initiate a multi-agent workflow
        - "provide_help": Provide guidance or help information
        - "request_clarification": Ask for more information

        Provide clear guidance and coordination.
        """

        result = await self.run(prompt)
        return result.data
    
    async def select_agent(self, request: str, context: Dict[str, Any] = None) -> AgentSelection:
        """Select the most appropriate agent for a specific request."""
        prompt = f"""
        Analyze the following request and select the most appropriate BMad agent:
        
        Request: {request}
        {f"Context: {context}" if context else ""}
        
        Available agents:
        - analyst: Requirements analysis, user story creation, business analysis
        - architect: System design, technical decisions, architecture planning
        - pm: Project planning, resource management, timeline creation
        - po: Product vision, backlog management, stakeholder requirements
        - sm: Process facilitation, team coordination, sprint planning
        - developer: Code implementation, technical execution, development tasks
        - qa: Quality assurance, testing, bug tracking
        - ux: User experience design, usability, user research
        - devops: Infrastructure, deployment, CI/CD, monitoring
        
        Select the best agent and provide rationale for your choice.
        Also suggest alternative agents if applicable.
        """
        
        temp_agent = BMadAgent(
            role="agent-selector",
            system_prompt="You are an agent selector for the BMad Method framework.",
            response_model=AgentSelection
        )

        result = await temp_agent.run(prompt)
        return result.data
    
    async def recommend_workflow(self, request: str, context: Dict[str, Any] = None) -> WorkflowRecommendation:
        """Recommend the most appropriate workflow for a complex request."""
        prompt = f"""
        Analyze the following request and recommend the most appropriate BMad workflow:
        
        Request: {request}
        {f"Context: {context}" if context else ""}
        
        Available workflows:
        - brownfield-fullstack: Enhance existing full-stack applications
        - greenfield-fullstack: Create new full-stack applications
        - brownfield-service: Enhance existing backend services
        - greenfield-service: Create new backend services
        - brownfield-ui: Enhance existing user interfaces
        - greenfield-ui: Create new user interfaces
        
        Recommend the best workflow and explain:
        - Why this workflow is appropriate
        - What inputs will be required
        - What outputs can be expected
        - Estimated duration/complexity
        """
        
        temp_agent = BMadAgent(
            role="workflow-recommender",
            system_prompt="You are a workflow recommender for the BMad Method framework.",
            response_model=WorkflowRecommendation
        )

        result = await temp_agent.run(prompt)
        return result.data
    
    async def execute_agent_request(self, agent_name: str, request: str, context: Dict[str, Any] = None) -> Any:
        """Execute request on specific agent."""
        agent = self._get_agent(agent_name)
        result = await agent.run(request)
        return result.data if hasattr(result, 'data') else result
    
    async def get_help(self, topic: str = None) -> str:
        """Provide help information about BMad Method and available agents."""
        if topic:
            prompt = f"Provide detailed help information about: {topic}"
        else:
            prompt = "Provide an overview of the BMad Method, available agents, and how to use them."
        
        class HelpResponse(BaseModel):
            help_content: str
        
        temp_agent = BMadAgent(
            role="help",
            system_prompt="You are a help assistant for the BMad Method framework.",
            response_model=HelpResponse
        )
        
        result = await temp_agent.run(prompt)
        return result.data.help_content
    
    async def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents and active workflows."""
        return {
            "available_agents": list(self.agents.keys()),
            "active_workflows": list(self.active_workflows.keys()),
            "orchestrator_role": self.role,
            "created_at": self.created_at.isoformat()
        }
    
    async def coordinate_handoff(self, from_agent: str, to_agent: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate handoff between agents with context preservation."""
        prompt = f"""
        Coordinate a handoff from {from_agent} agent to {to_agent} agent.
        
        Context to transfer: {context}
        
        Provide:
        1. Summary of what was accomplished by {from_agent}
        2. What needs to be communicated to {to_agent}
        3. Any context that needs to be preserved
        4. Next steps for {to_agent}
        """
        
        class HandoffResponse(BaseModel):
            summary: str
            communication_to_next_agent: str
            preserved_context: Dict[str, Any]
            next_steps: List[str]
        
        temp_agent = BMadAgent(
            role="handoff-coordinator",
            system_prompt="You are a handoff coordinator for the BMad Method framework.",
            response_model=HandoffResponse
        )
        
        result = await temp_agent.run(prompt)
        return result.data.model_dump()