# Project Structure & Organization

## Root Directory Layout

```
BMADPydanticAgents/
├── bmad_agents/              # Main package - BMad agent implementations
├── bmad_ui/                  # UI components (backend/frontend/static)
├── docs/                     # Documentation (PRD, architecture, guides)
├── tests/                    # Test suite (unit, integration, workflow tests)
├── logs/                     # Application logs (agents, audit, system, workflows)
├── venv/                     # Python virtual environment
├── .bmad-core/              # BMad framework core components
├── .bmad_state/             # Runtime state management
├── .kiro/                   # Kiro IDE configuration and steering
├── .trae/                   # Additional tooling rules
└── [root files]            # Config, examples, and entry points
```

## Core Package Structure (`bmad_agents/`)

```
bmad_agents/
├── __init__.py              # Package exports and initialization
├── base/                    # Core infrastructure
│   ├── __init__.py
│   ├── bmad_agent.py       # Base BMadAgent class
│   ├── communication.py    # Inter-agent communication
│   ├── config.py           # Configuration management
│   ├── error_handling.py   # Error handling and retries
│   ├── logging_config.py   # Logging infrastructure
│   ├── models.py           # Shared Pydantic models
│   ├── monitoring.py       # Performance monitoring
│   └── state_manager.py    # Workflow state management
├── agents/                  # Individual agent implementations
│   ├── __init__.py
│   ├── analyst.py          # Requirements analysis agent
│   ├── architect.py        # System architecture agent
│   ├── pm.py               # Project manager agent
│   ├── po.py               # Product owner agent
│   ├── sm.py               # Scrum master agent
│   ├── dev.py              # Developer agent
│   ├── qa.py               # Quality assurance agent
│   ├── ux_expert.py        # UX expert agent
│   └── orchestrator.py     # Central orchestration agent
├── workflows/               # Workflow implementations
│   ├── __init__.py
│   ├── base_workflow.py    # Base workflow class
│   ├── brownfield_fullstack.py  # Brownfield enhancement workflow
│   └── greenfield_fullstack.py  # Greenfield development workflow
└── examples/                # Usage examples and demos
    ├── __init__.py
    ├── single_agent_demo.py
    ├── workflow_demo.py
    └── orchestrator_demo.py
```

## Documentation Structure (`docs/`)

```
docs/
├── prd.md                   # Product Requirements Document
├── architecture.md         # Technical architecture overview
├── bmad_agents_guide.md    # User guide and API reference
├── story_breakdown.md      # Development story breakdown
├── story_*_completion.md   # Individual story completion docs
└── SETUP_COMPLETE.md       # Setup verification document
```

## Test Structure (`tests/`)

```
tests/
├── __init__.py
├── test_agents.py          # Individual agent tests
├── test_integration.py     # Integration tests
├── test_workflows.py       # Workflow tests
└── __pycache__/           # Python bytecode cache
```

## Configuration Files

### Root Level Configuration
- **`.env`**: Environment variables and API keys (gitignored)
- **`.env.example`**: Template for environment configuration
- **`requirements.txt`**: Python dependencies
- **`README.md`**: Project overview and setup instructions

### Example Files (Root Level)
- **`example_agent.py`**: Basic Pydantic AI weather agent example
- **`gemini_example.py`**: Google Gemini integration examples
- **`gemini_2_advanced.py`**: Advanced Gemini demonstrations
- **`quick_start.py`**: Quick start script for new users

### Test Files (Root Level)
- **`test_pydantic_ai.py`**: Basic Pydantic AI installation test
- **`test_gemini.py`**: Gemini API connection test
- **`test_bmad_base.py`**: BMad base functionality test

## Naming Conventions

### Files and Directories
- **Python Files**: `snake_case.py` (e.g., `bmad_agent.py`, `error_handling.py`)
- **Directories**: `snake_case` (e.g., `bmad_agents`, `base`)
- **Test Files**: `test_*.py` prefix (e.g., `test_agents.py`)
- **Example Files**: `*_example.py` or `*_demo.py` suffix

### Code Elements
- **Classes**: `PascalCase` (e.g., `BMadAgent`, `AnalystAgent`)
- **Functions/Methods**: `snake_case` (e.g., `analyze_requirements`, `create_user_stories`)
- **Constants**: `UPPER_SNAKE_CASE` (e.g., `DEFAULT_MODEL`, `MAX_RETRIES`)
- **Variables**: `snake_case` (e.g., `agent_response`, `workflow_state`)

## Import Patterns

### Package-Level Imports
```python
# From package root
from bmad_agents import BMadOrchestrator, AnalystAgent
from bmad_agents.base import BMadAgent, StateManager
from bmad_agents.workflows import BrownfieldFullstackWorkflow
```

### Internal Module Imports
```python
# Within bmad_agents package
from .base.bmad_agent import BMadAgent
from .base.models import AgentMessage, WorkflowState
from ..base.error_handling import BMadError, with_retry
```

### External Dependencies
```python
# Standard library first
import os
import asyncio
from datetime import datetime
from typing import List, Dict, Optional

# Third-party packages
from pydantic import BaseModel, Field
from pydantic_ai import Agent
from dotenv import load_dotenv

# Local imports last
from bmad_agents.base import BMadAgent
```

## File Organization Principles

### Separation of Concerns
- **Base Infrastructure**: Core functionality shared across all agents
- **Agent Implementations**: Role-specific agent logic and prompts
- **Workflows**: Multi-agent orchestration and process management
- **Examples**: Demonstration and tutorial code

### Backward Compatibility
- **Existing Examples**: Maintained in root directory unchanged
- **New Functionality**: Organized under `bmad_agents/` package
- **Configuration**: Extends existing `.env` approach

### Scalability Considerations
- **Modular Design**: Each agent in separate file for independent development
- **Clear Interfaces**: Well-defined APIs between components
- **Extensibility**: Easy to add new agents, workflows, and capabilities

## State and Data Management

### Runtime State (`logs/`, `.bmad_state/`)
- **Application Logs**: Structured logging in `logs/` directory
- **Workflow State**: Persistent workflow state in `.bmad_state/`
- **Agent State**: Runtime agent context and memory

### Configuration State (`.kiro/`, `.bmad-core/`)
- **IDE Configuration**: Kiro-specific settings and steering rules
- **BMad Framework**: Core BMad method components and templates
- **Development Tools**: Additional tooling and automation rules

This structure supports both the existing Pydantic AI examples and the new BMad agent system while maintaining clear separation of concerns and enabling future extensibility.