{"workflow_id": "7d4a330b-ada2-40b5-b83b-fd704e6e5b9d", "workflow_type": "brownfield-fullstack", "current_step": "scope_classification", "status": "active", "progress": 0.0, "started_at": "2025-08-03 16:17:00.421333", "updated_at": "2025-08-03 16:17:00.421847", "context": {"enhancement_description": "test enhancement", "project_context": {"test": "data"}}, "participants": [], "completed_steps": [], "pending_steps": ["documentation_check", "project_analysis", "prd_creation", "architecture_decisions", "story_creation"], "agent_contexts": {}, "shared_context": {"enhancement_description": "test enhancement", "project_context": {"test": "data"}}}