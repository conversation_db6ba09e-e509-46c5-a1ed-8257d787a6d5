# Technology Stack & Build System

## Core Technologies

### Language & Runtime
- **Python**: 3.12.2 (required)
- **Virtual Environment**: `venv/` (standard Python venv)
- **Package Management**: pip with requirements.txt

### AI Framework
- **Primary Framework**: Pydantic AI v0.4.11
- **Data Validation**: Pydantic v2.11.7
- **Response Models**: Pydantic BaseModel for all agent responses

### AI Model Providers
- **Primary**: Google Gemini 2.5 Flash (`gemini-2.5-flash`)
- **Secondary**: OpenAI GPT-4o-mini (`gpt-4o-mini`)
- **Supported**: <PERSON><PERSON><PERSON>, <PERSON>roq, Mistral AI, Cohere, AWS Bedrock

### Key Dependencies
- **Environment**: python-dotenv for configuration
- **Async**: asyncio for concurrent operations
- **HTTP**: httpx, aiohttp for API calls
- **Logging**: Built-in logging with structured output
- **Testing**: pytest, pytest-asyncio for async testing

## Build System & Commands

### Environment Setup
```bash
# Activate virtual environment
# Windows
venv\Scripts\activate
# Linux/Mac  
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### Testing
```bash
# Run all tests
pytest tests/ -v

# Run specific test categories
pytest tests/test_agents.py -v          # Agent tests
pytest tests/test_integration.py -v     # Integration tests
pytest tests/test_workflows.py -v       # Workflow tests

# Run with coverage
pytest tests/ --cov=bmad_agents --cov-report=html
```

### Development Commands
```bash
# Test basic installation
python test_pydantic_ai.py

# Test API connections
python test_gemini.py

# Run example agents
python example_agent.py
python gemini_example.py
python gemini_2_advanced.py

# Run BMad agent examples
python bmad_agents/examples/single_agent_demo.py
python bmad_agents/examples/workflow_demo.py
```

### Code Quality
```bash
# Type checking (if mypy installed)
mypy bmad_agents/

# Code formatting (if black installed)
black bmad_agents/

# Linting (if flake8 installed)
flake8 bmad_agents/
```

## Configuration Management

### Environment Variables (.env)
```bash
# Required API Keys
GOOGLE_AI_API_KEY=your_google_ai_key
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# BMad-specific settings
BMAD_DEFAULT_MODEL=gemini-2.5-flash
BMAD_AGENT_TIMEOUT=30
BMAD_MAX_CONCURRENT_AGENTS=5
BMAD_LOG_LEVEL=INFO
```

### Model Configuration
- **Default Model**: `gemini-2.5-flash` (latest Gemini)
- **Fallback Model**: `gpt-4o-mini` (OpenAI)
- **Model Selection**: Configurable per agent via environment variables

## Architecture Patterns

### Agent Pattern
- **Base Class**: `BMadAgent` extends Pydantic AI `Agent`
- **Specialization**: Role-specific agents inherit from base
- **Communication**: Pydantic models for structured data exchange
- **State Management**: Centralized state management with persistence

### Async/Await Pattern
- **All Operations**: Async by default for non-blocking execution
- **Concurrency**: Support for multiple concurrent agent operations
- **Error Handling**: Async-compatible error handling with retries

### Configuration Pattern
- **Environment-based**: All configuration via environment variables
- **Hierarchical**: Global defaults, agent-specific overrides
- **Runtime**: Dynamic configuration changes without restart

## Development Standards

### Code Organization
- **Modules**: Clear separation by functionality (agents/, workflows/, base/)
- **Imports**: Absolute imports from package root
- **Dependencies**: Minimal external dependencies, prefer standard library

### Error Handling
- **Custom Exceptions**: BMad-specific exception hierarchy
- **Graceful Degradation**: Fallback mechanisms for API failures
- **Logging**: Comprehensive error logging with context

### Performance Considerations
- **Connection Pooling**: Reuse HTTP connections for API calls
- **Rate Limiting**: Built-in rate limiting for API providers
- **Memory Management**: Efficient state management and cleanup