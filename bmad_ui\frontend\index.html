<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BMad Agents - AI-Powered Development Team</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="connection-status" id="connectionStatus">Connecting...</div>
    
    <div class="container">
        <div class="sidebar">
            <h2>🎭 BMad Agents</h2>
            <ul class="agent-list" id="agentList">
                <!-- Agents will be loaded dynamically -->
            </ul>
        </div>
        
        <div class="main-content">
            <div class="header">
                <h1>BMad Method AI Agents</h1>
                <p>Your AI-powered development team for brownfield applications</p>
            </div>
            
            <div class="chat-container">
                <div class="current-agent" id="currentAgent" style="display: none;">
                    <h3 id="currentAgentName">Select an Agent</h3>
                    <p id="currentAgentDescription">Choose an agent from the sidebar to start a conversation</p>
                </div>
                
                <div class="messages" id="messages">
                    <div class="welcome-message">
                        <h3>Welcome to BMad Agents! 🚀</h3>
                        <p>Select an agent from the sidebar to start collaborating with your AI development team.</p>
                        <p>Each agent specializes in different aspects of software development:</p>
                        <p><strong>Orchestrator</strong> - Coordinates all agents and workflows</p>
                        <p><strong>Analyst</strong> - Analyzes requirements and creates user stories</p>
                        <p><strong>Architect</strong> - Designs system architecture</p>
                        <p><strong>Developer</strong> - Implements code and technical solutions</p>
                        <p>...and many more!</p>
                    </div>
                </div>
                
                <div class="typing-indicator" id="typingIndicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
                
                <div class="input-container">
                    <input type="text" class="message-input" id="messageInput" placeholder="Type your message..." disabled>
                    <button class="send-button" id="sendButton" disabled>Send</button>
                </div>
            </div>
        </div>
        
        <div class="workflow-panel">
            <h3>Available Workflows</h3>
            <div id="workflowList">
                <!-- Workflows will be loaded dynamically -->
            </div>
        </div>
    </div>

    <script>
        class BMadAgentsUI {
            constructor() {
                this.sessionId = this.generateSessionId();
                this.currentAgent = null;
                this.websocket = null;
                this.agents = [];
                this.workflows = [];
                
                this.initializeElements();
                this.loadAgents();
                this.loadWorkflows();
                this.connectWebSocket();
                this.setupEventListeners();
            }
            
            generateSessionId() {
                return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            }
            
            initializeElements() {
                this.elements = {
                    connectionStatus: document.getElementById('connectionStatus'),
                    agentList: document.getElementById('agentList'),
                    currentAgent: document.getElementById('currentAgent'),
                    currentAgentName: document.getElementById('currentAgentName'),
                    currentAgentDescription: document.getElementById('currentAgentDescription'),
                    messages: document.getElementById('messages'),
                    typingIndicator: document.getElementById('typingIndicator'),
                    messageInput: document.getElementById('messageInput'),
                    sendButton: document.getElementById('sendButton'),
                    workflowList: document.getElementById('workflowList')
                };
            }
            
            async loadAgents() {
                try {
                    const response = await fetch('/api/agents');
                    this.agents = await response.json();
                    this.renderAgents();
                } catch (error) {
                    console.error('Failed to load agents:', error);
                }
            }
            
            async loadWorkflows() {
                try {
                    const response = await fetch('/api/workflows');
                    this.workflows = await response.json();
                    this.renderWorkflows();
                } catch (error) {
                    console.error('Failed to load workflows:', error);
                }
            }
            
            renderAgents() {
                this.elements.agentList.innerHTML = '';
                
                this.agents.forEach(agent => {
                    const li = document.createElement('li');
                    li.className = 'agent-item';
                    li.dataset.agentName = agent.name;
                    
                    li.innerHTML = `
                        <div class="agent-icon">${agent.icon}</div>
                        <div class="agent-name">${agent.role}</div>
                        <div class="agent-description">${agent.description}</div>
                    `;
                    
                    li.addEventListener('click', () => this.selectAgent(agent));
                    this.elements.agentList.appendChild(li);
                });
            }
            
            renderWorkflows() {
                this.elements.workflowList.innerHTML = '';
                
                this.workflows.forEach(workflow => {
                    const div = document.createElement('div');
                    div.className = 'workflow-item';
                    
                    div.innerHTML = `
                        <div class="workflow-name">${workflow.name}</div>
                        <div class="workflow-description">${workflow.description}</div>
                    `;
                    
                    div.addEventListener('click', () => this.startWorkflow(workflow));
                    this.elements.workflowList.appendChild(div);
                });
            }
            
            selectAgent(agent) {
                // Update UI
                document.querySelectorAll('.agent-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                document.querySelector(`[data-agent-name="${agent.name}"]`).classList.add('active');
                
                this.currentAgent = agent;
                this.elements.currentAgent.style.display = 'block';
                this.elements.currentAgentName.textContent = `${agent.icon} ${agent.role}`;
                this.elements.currentAgentDescription.textContent = agent.description;
                
                // Enable input
                this.elements.messageInput.disabled = false;
                this.elements.sendButton.disabled = false;
                this.elements.messageInput.placeholder = `Chat with ${agent.role}...`;
                this.elements.messageInput.focus();
                
                // Clear welcome message
                const welcomeMessage = this.elements.messages.querySelector('.welcome-message');
                if (welcomeMessage) {
                    welcomeMessage.remove();
                }
            }
            
            startWorkflow(workflow) {
                if (this.currentAgent?.name !== 'orchestrator') {
                    // Switch to orchestrator for workflow management
                    const orchestrator = this.agents.find(a => a.name === 'orchestrator');
                    if (orchestrator) {
                        this.selectAgent(orchestrator);
                    }
                }
                
                // Send workflow start message
                const message = `Start ${workflow.name} workflow`;
                this.elements.messageInput.value = message;
                this.sendMessage();
            }
            
            connectWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws/${this.sessionId}`;
                
                this.websocket = new WebSocket(wsUrl);
                
                this.websocket.onopen = () => {
                    this.updateConnectionStatus(true);
                };
                
                this.websocket.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                };
                
                this.websocket.onclose = () => {
                    this.updateConnectionStatus(false);
                    // Attempt to reconnect after 3 seconds
                    setTimeout(() => this.connectWebSocket(), 3000);
                };
                
                this.websocket.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    this.updateConnectionStatus(false);
                };
            }
            
            updateConnectionStatus(connected) {
                const status = this.elements.connectionStatus;
                if (connected) {
                    status.textContent = 'Connected';
                    status.className = 'connection-status connected';
                } else {
                    status.textContent = 'Disconnected';
                    status.className = 'connection-status disconnected';
                }
            }
            
            handleWebSocketMessage(data) {
                switch (data.type) {
                    case 'message':
                        this.addMessage(data.message, 'agent', data.agent);
                        this.hideTypingIndicator();
                        break;
                    case 'typing':
                        this.showTypingIndicator();
                        break;
                    case 'error':
                        this.addMessage(data.message, 'agent', 'system');
                        this.hideTypingIndicator();
                        break;
                }
            }
            
            setupEventListeners() {
                this.elements.sendButton.addEventListener('click', () => this.sendMessage());
                
                this.elements.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }
            
            sendMessage() {
                const message = this.elements.messageInput.value.trim();
                if (!message || !this.currentAgent) return;
                
                // Add user message to chat
                this.addMessage(message, 'user');
                
                // Clear input
                this.elements.messageInput.value = '';
                
                // Send via WebSocket
                if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                    this.websocket.send(JSON.stringify({
                        type: 'chat',
                        message: message,
                        agent: this.currentAgent.name,
                        session_id: this.sessionId
                    }));
                }
            }
            
            addMessage(content, type, agent = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                
                const timestamp = new Date().toLocaleTimeString();
                const agentName = agent ? this.agents.find(a => a.name === agent)?.role || agent : '';
                
                messageDiv.innerHTML = `
                    <div class="message-header">
                        ${type === 'user' ? 'You' : agentName} • ${timestamp}
                    </div>
                    <div class="message-content">${this.formatMessage(content)}</div>
                `;
                
                this.elements.messages.appendChild(messageDiv);
                this.elements.messages.scrollTop = this.elements.messages.scrollHeight;
            }
            
            formatMessage(content) {
                // Basic formatting for better readability
                return content
                    .replace(/\n/g, '<br>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/`(.*?)`/g, '<code>$1</code>');
            }
            
            showTypingIndicator() {
                this.elements.typingIndicator.style.display = 'block';
                this.elements.messages.scrollTop = this.elements.messages.scrollHeight;
            }
            
            hideTypingIndicator() {
                this.elements.typingIndicator.style.display = 'none';
            }
        }
        
        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            new BMadAgentsUI();
        });
    </script>
</body>
</html>