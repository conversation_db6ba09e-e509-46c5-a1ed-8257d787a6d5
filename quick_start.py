#!/usr/bin/env python3
"""
Quick Start Guide for BMad Pydantic AI Agents

This script demonstrates how to get started with the BMad Agents system.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path.cwd()))

def check_environment():
    """Check if the environment is properly set up."""
    print("🔍 Checking BMad Agents Environment...")
    print("=" * 50)
    
    # Check Python version
    print(f"✅ Python Version: {sys.version.split()[0]}")
    
    # Check if virtual environment is active
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual Environment: Active")
    else:
        print("⚠️  Virtual Environment: Not detected (recommended to use venv)")
    
    # Check if .env file exists
    env_file = Path('.env')
    if env_file.exists():
        print("✅ Environment File: Found (.env)")
        # Check for API key
        with open(env_file, 'r') as f:
            content = f.read()
            if 'GOOGLE_AI_API_KEY=' in content and 'your_google_ai_api_key_here' not in content:
                print("✅ Google AI API Key: Configured")
            else:
                print("⚠️  Google AI API Key: Not configured or using placeholder")
    else:
        print("❌ Environment File: Missing (.env file not found)")
    
    # Check if bmad_agents package can be imported
    try:
        import bmad_agents
        print("✅ BMad Agents Package: Available")
        
        # Try to import key components
        from bmad_agents.agents.orchestrator import BMadOrchestrator
        from bmad_agents.agents.analyst import AnalystAgent
        from bmad_agents.agents.architect import ArchitectAgent
        print("✅ Core Agents: Available (Orchestrator, Analyst, Architect)")
        
    except ImportError as e:
        print(f"❌ BMad Agents Package: Import Error - {e}")
        print("   💡 Try running: $env:PYTHONPATH = '.'; python quick_start.py")
        return False
    
    return True

def show_usage_examples():
    """Show basic usage examples."""
    print("\n📚 Basic Usage Examples")
    print("=" * 50)
    
    print("""
1. **Using Individual Agents:**
   
   from bmad_agents.agents.analyst import AnalystAgent
   
   analyst = AnalystAgent()
   result = await analyst.analyze_requirements("Create a web app for task management")
   print(result.summary)

2. **Using the Orchestrator:**
   
   from bmad_agents.agents.orchestrator import BMadOrchestrator
   
   orchestrator = BMadOrchestrator()
   response = await orchestrator.route_request("I need help analyzing requirements")
   print(f"Routed to: {response.target_agent}")

3. **Using Workflows:**
   
   from bmad_agents.workflows.brownfield_fullstack import BrownfieldFullstackWorkflow
   
   workflow = BrownfieldFullstackWorkflow()
   state = await workflow.start_workflow({
       "project_name": "My Project",
       "enhancement_description": "Add user authentication"
   })
   print(f"Workflow started: {state.workflow_id}")
""")

def show_available_commands():
    """Show available commands to run."""
    print("\n🚀 Available Commands")
    print("=" * 50)
    
    print("""
**Set Python Path and Run Examples:**

# PowerShell (Windows):
$env:PYTHONPATH = "."
python bmad_agents/examples/complete_example.py

# Or run individual components:
python -c "import sys; sys.path.insert(0, '.'); from bmad_agents.agents.analyst import AnalystAgent; print('Analyst Agent available!')"

**Available Example Files:**
• bmad_agents/examples/complete_example.py - Full system demonstration
• bmad_agents/examples/basic_example.py - Basic infrastructure demo

**Available Agents:**
• AnalystAgent - Requirements analysis and user story creation
• ArchitectAgent - System architecture and technical design
• BMadOrchestrator - Request routing and agent coordination
• Plus: PM, PO, SM, Developer, QA, UX, DevOps agents

**Available Workflows:**
• BrownfieldFullstackWorkflow - Modernize existing applications
• BaseWorkflow - Foundation for custom workflows
""")

def main():
    """Main function to run the quick start guide."""
    print("🎯 BMad Pydantic AI Agents - Quick Start Guide")
    print("=" * 60)
    
    # Check environment
    env_ok = check_environment()
    
    # Show usage examples
    show_usage_examples()
    
    # Show available commands
    show_available_commands()
    
    if env_ok:
        print("\n✅ **System Status: Ready to Use!**")
        print("\n💡 **Next Steps:**")
        print("   1. Make sure your Google AI API key is configured in .env")
        print("   2. Run: $env:PYTHONPATH = '.'; python bmad_agents/examples/complete_example.py")
        print("   3. Explore the agents and workflows for your specific needs")
    else:
        print("\n❌ **System Status: Setup Required**")
        print("\n🔧 **Setup Steps:**")
        print("   1. Ensure you're in the project directory")
        print("   2. Activate virtual environment: venv\\Scripts\\activate")
        print("   3. Install dependencies: pip install -r requirements.txt")
        print("   4. Configure .env file with your API keys")
        print("   5. Run this script again to verify setup")
    
    print("\n" + "=" * 60)
    print("📖 For detailed documentation, see: docs/bmad_agents_guide.md")
    print("🔗 Project structure and examples in: bmad_agents/")

if __name__ == "__main__":
    main()