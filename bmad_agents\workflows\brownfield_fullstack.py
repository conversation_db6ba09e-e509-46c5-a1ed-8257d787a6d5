from typing import Dict, List, Any
from .base_workflow import BaseWorkflow
from ..base.models import WorkflowStep, WorkflowState
from ..agents.analyst import AnalystAgent
from ..agents.architect import ArchitectAgent
from ..agents.po import POAgent
from ..agents.pm import PMAgent
from ..agents.developer import DeveloperAgent
from ..agents.qa import QAAgent

class BrownfieldFullstackWorkflow(BaseWorkflow):
    """Workflow for enhancing existing full-stack applications."""
    
    def __init__(self):
        super().__init__("brownfield-fullstack")
        
        # Initialize agents
        self.analyst = AnalystAgent()
        self.architect = ArchitectAgent()
        self.po = POAgent()
        self.pm = PMAgent()
        self.developer = DeveloperAgent()
        self.qa = QAAgent()
    
    def _define_steps(self) -> List[WorkflowStep]:
        return [
            WorkflowStep(
                step_id="scope_classification",
                step_name="Scope Classification",
                required_agents=["analyst"],
                input_requirements={"enhancement_description": "string"},
                output_specifications={"scope_type": "string", "complexity": "string"}
            ),
            WorkflowStep(
                step_id="documentation_check",
                step_name="Documentation Assessment",
                required_agents=["analyst"],
                input_requirements={"project_context": "dict"},
                output_specifications={"documentation_status": "dict", "gaps": "list"}
            ),
            WorkflowStep(
                step_id="project_analysis",
                step_name="Project Analysis",
                required_agents=["analyst", "architect"],
                input_requirements={"codebase_info": "dict"},
                output_specifications={"analysis_report": "dict", "recommendations": "list"}
            ),
            WorkflowStep(
                step_id="prd_creation",
                step_name="PRD Creation",
                required_agents=["analyst", "po"],
                input_requirements={"requirements": "dict"},
                output_specifications={"prd_document": "dict"}
            ),
            WorkflowStep(
                step_id="architecture_decisions",
                step_name="Architecture Decisions",
                required_agents=["architect"],
                input_requirements={"prd": "dict", "current_architecture": "dict"},
                output_specifications={"architecture_plan": "dict", "decisions": "list"}
            ),
            WorkflowStep(
                step_id="story_creation",
                step_name="Story Creation",
                required_agents=["analyst", "po"],
                input_requirements={"prd": "dict", "architecture": "dict"},
                output_specifications={"user_stories": "list", "backlog": "dict"}
            )
        ]
    
    async def _execute_step_logic(self, step: WorkflowStep, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute step-specific logic."""
        
        if step.step_id == "scope_classification":
            return await self._execute_scope_classification(step_input, state)
        elif step.step_id == "documentation_check":
            return await self._execute_documentation_check(step_input, state)
        elif step.step_id == "project_analysis":
            return await self._execute_project_analysis(step_input, state)
        elif step.step_id == "prd_creation":
            return await self._execute_prd_creation(step_input, state)
        elif step.step_id == "architecture_decisions":
            return await self._execute_architecture_decisions(step_input, state)
        elif step.step_id == "story_creation":
            return await self._execute_story_creation(step_input, state)
        else:
            raise ValueError(f"Unknown step: {step.step_id}")
    
    async def _execute_scope_classification(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute scope classification step."""
        enhancement_description = step_input.get("enhancement_description", "")
        
        analysis = await self.analyst.analyze_requirements(
            f"Classify the scope and complexity of this enhancement: {enhancement_description}",
            context=state.shared_context
        )
        
        # Determine scope type based on analysis
        scope_type = "major_enhancement"  # This would be determined by analysis logic
        complexity = "high"  # This would be determined by analysis logic
        
        return {
            "scope_type": scope_type,
            "complexity": complexity,
            "analysis": analysis.output if hasattr(analysis, 'output') else str(analysis),
            "context_updates": {
                "scope_classification": {
                    "type": scope_type,
                    "complexity": complexity,
                    "analysis": analysis.output if hasattr(analysis, 'output') else str(analysis)
                }
            }
        }
    
    async def _execute_documentation_check(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute documentation assessment step."""
        project_context = step_input.get("project_context", {})
        
        # Use analyst to assess documentation status
        assessment_prompt = f"""
        Assess the documentation status for this project:
        Project Context: {project_context}
        
        Evaluate:
        1. Existing documentation completeness
        2. Documentation gaps
        3. Required documentation updates
        4. Documentation quality assessment
        """
        
        analysis = await self.analyst.analyze_requirements(
            assessment_prompt,
            context=state.shared_context
        )
        
        documentation_status = {
            "completeness": "partial",
            "quality": "medium",
            "coverage": ["api", "user_guide"]
        }
        
        gaps = [
            "Architecture documentation",
            "Deployment guide",
            "Testing documentation"
        ]
        
        return {
            "documentation_status": documentation_status,
            "gaps": gaps,
            "assessment": analysis.output if hasattr(analysis, 'output') else str(analysis),
            "context_updates": {
                "documentation_assessment": {
                    "status": documentation_status,
                    "gaps": gaps,
                    "analysis": analysis.output if hasattr(analysis, 'output') else str(analysis)
                }
            }
        }
    
    async def _execute_project_analysis(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute project analysis step."""
        codebase_info = step_input.get("codebase_info", {})
        
        # Analyst analyzes requirements
        analyst_prompt = f"""
        Analyze this codebase for enhancement opportunities:
        Codebase Info: {codebase_info}
        Previous Analysis: {state.shared_context.get('scope_classification', {})}
        
        Provide detailed analysis of:
        1. Current system capabilities
        2. Enhancement feasibility
        3. Risk assessment
        4. Resource requirements
        """
        
        analyst_result = await self.analyst.analyze_requirements(
            analyst_prompt,
            context=state.shared_context
        )
        
        # Architect reviews technical aspects
        architect_prompt = f"""
        Review the technical architecture for this enhancement:
        Codebase: {codebase_info}
        Requirements Analysis: {analyst_result.output if hasattr(analyst_result, 'output') else str(analyst_result)}

        Assess:
        1. Technical feasibility
        2. Architecture impact
        3. Integration points
        4. Technical risks
        """

        architect_result = await self.architect.design_architecture(
            architect_prompt,
            constraints=codebase_info
        )

        analysis_report = {
            "analyst_findings": analyst_result.output if hasattr(analyst_result, 'output') else str(analyst_result),
            "architect_assessment": architect_result.output if hasattr(architect_result, 'output') else str(architect_result),
            "overall_feasibility": "high",
            "estimated_effort": "medium"
        }
        
        recommendations = [
            "Implement in phases",
            "Update documentation first",
            "Add comprehensive testing"
        ]
        
        return {
            "analysis_report": analysis_report,
            "recommendations": recommendations,
            "context_updates": {
                "project_analysis": {
                    "report": analysis_report,
                    "recommendations": recommendations
                }
            }
        }
    
    async def _execute_prd_creation(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute PRD creation step."""
        requirements = step_input.get("requirements", {})
        
        # Combine analyst and PO expertise
        prd_prompt = f"""
        Create a comprehensive Product Requirements Document (PRD) based on:
        Requirements: {requirements}
        Project Analysis: {state.shared_context.get('project_analysis', {})}
        Scope Classification: {state.shared_context.get('scope_classification', {})}
        
        Include:
        1. Executive Summary
        2. Problem Statement
        3. Solution Overview
        4. Feature Requirements
        5. Success Metrics
        6. Timeline and Milestones
        """
        
        # Use PO to create PRD
        prd_result = await self.po.create_prd(
            prd_prompt,
            context=state.shared_context
        )

        prd_document = prd_result.output if hasattr(prd_result, 'output') else str(prd_result)

        return {
            "prd_document": prd_document,
            "context_updates": {
                "prd": prd_document
            }
        }
    
    async def _execute_architecture_decisions(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute architecture decisions step."""
        prd = step_input.get("prd", state.shared_context.get("prd", {}))
        current_architecture = step_input.get("current_architecture", {})
        
        architecture_prompt = f"""
        Design architecture decisions for this enhancement:
        PRD: {prd}
        Current Architecture: {current_architecture}
        Project Analysis: {state.shared_context.get('project_analysis', {})}
        
        Provide:
        1. Architecture modifications needed
        2. New components required
        3. Integration approach
        4. Technology decisions
        5. Migration strategy
        """
        
        architecture_result = await self.architect.design_architecture(
            architecture_prompt,
            constraints=current_architecture
        )

        architecture_plan = architecture_result.output if hasattr(architecture_result, 'output') else str(architecture_result)
        # Handle the case where architecture_result might not have architectural_decisions attribute
        decisions = getattr(architecture_result, 'architectural_decisions', []) if hasattr(architecture_result, 'architectural_decisions') else []

        return {
            "architecture_plan": architecture_plan,
            "decisions": [decision.model_dump() if hasattr(decision, 'model_dump') else str(decision) for decision in decisions],
            "context_updates": {
                "architecture": {
                    "plan": architecture_plan,
                    "decisions": [decision.model_dump() if hasattr(decision, 'model_dump') else str(decision) for decision in decisions]
                }
            }
        }
    
    async def _execute_story_creation(self, step_input: Dict[str, Any], state: WorkflowState) -> Dict[str, Any]:
        """Execute story creation step."""
        prd = step_input.get("prd", state.shared_context.get("prd", {}))
        architecture = step_input.get("architecture", state.shared_context.get("architecture", {}))
        
        # Create user stories based on PRD and architecture
        story_prompt = f"""
        Create detailed user stories based on:
        PRD: {prd}
        Architecture Plan: {architecture}
        
        Generate:
        1. Epic-level stories
        2. Detailed user stories with acceptance criteria
        3. Technical stories for architecture changes
        4. Story prioritization
        5. Sprint planning recommendations
        """
        
        # Use analyst to create stories
        analyst_result = await self.analyst.analyze_requirements(
            story_prompt,
            context=state.shared_context
        )
        
        # Use PO to refine and prioritize
        po_prompt = f"""
        Refine and prioritize these user stories:
        Initial Stories: {analyst_result.output if hasattr(analyst_result, 'output') else str(analyst_result)}

        Provide:
        1. Refined user stories
        2. Priority ranking
        3. Sprint breakdown
        4. Dependencies mapping
        """

        po_result = await self.po.create_user_stories(
            po_prompt,
            context=state.shared_context
        )

        user_stories = po_result.output if hasattr(po_result, 'output') else po_result
        # Handle the case where user_stories might not be a list or might not have priority attributes
        if isinstance(user_stories, list):
            backlog = {
                "total_stories": len(user_stories),
                "high_priority": [story for story in user_stories if hasattr(story, 'priority') and story.priority == "High"],
                "medium_priority": [story for story in user_stories if hasattr(story, 'priority') and story.priority == "Medium"],
                "low_priority": [story for story in user_stories if hasattr(story, 'priority') and story.priority == "Low"]
            }
        else:
            backlog = {"total_stories": 0, "high_priority": [], "medium_priority": [], "low_priority": []}

        return {
            "user_stories": [story.model_dump() if hasattr(story, 'model_dump') else str(story) for story in user_stories] if isinstance(user_stories, list) else str(user_stories),
            "backlog": backlog,
            "context_updates": {
                "stories": {
                    "user_stories": [story.model_dump() if hasattr(story, 'model_dump') else str(story) for story in user_stories] if isinstance(user_stories, list) else str(user_stories),
                    "backlog": backlog
                }
            }
        }