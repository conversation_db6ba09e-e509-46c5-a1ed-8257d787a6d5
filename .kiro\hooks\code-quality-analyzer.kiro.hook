{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and analyzes modified code for potential improvements including code smells, design patterns, and best practices", "version": "1", "when": {"type": "userTriggered", "patterns": ["*.py", "bmad_agents/**/*.py", "tests/**/*.py", "*.js", "*.ts", "*.java", "*.cpp", "*.c", "*.h", "*.cs", "*.rb", "*.go", "*.rs"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code in the changed files for potential improvements. Focus on:\n\n1. **Code Smells**: Identify any code smells such as long methods, large classes, duplicate code, or complex conditionals\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n3. **Best Practices**: Check adherence to language-specific best practices and coding standards\n4. **Readability**: Suggest improvements for variable naming, code organization, and documentation\n5. **Maintainability**: Identify areas that could be refactored for better maintainability\n6. **Performance**: Suggest performance optimizations where applicable\n\nProvide specific, actionable suggestions while ensuring the existing functionality is preserved. Format your response with clear sections for each type of improvement and include code examples where helpful."}}