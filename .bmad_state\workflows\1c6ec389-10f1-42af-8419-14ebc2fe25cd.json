{"workflow_id": "1c6ec389-10f1-42af-8419-14ebc2fe25cd", "workflow_type": "brownfield-fullstack", "current_step": "scope_classification", "status": "active", "progress": 0.0, "started_at": "2025-08-03 16:16:45.265432", "updated_at": "2025-08-03 16:16:45.265936", "context": {"enhancement_description": "test enhancement", "project_context": {"test": "data"}}, "participants": [], "completed_steps": [], "pending_steps": ["documentation_check", "project_analysis", "prd_creation", "architecture_decisions", "story_creation"], "agent_contexts": {}, "shared_context": {"enhancement_description": "test enhancement", "project_context": {"test": "data"}}}