#!/usr/bin/env python3
"""
Simple test script to demonstrate BMad Agents functionality
"""

import asyncio
import sys
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path.cwd()))

async def test_analyst_agent():
    """Test the AnalystAgent functionality."""
    print("🔍 Testing AnalystAgent...")
    
    try:
        from bmad_agents.agents.analyst import AnalystAgent
        
        # Create agent
        agent = AnalystAgent()
        print("✅ AnalystAgent created successfully!")
        
        # Get agent info
        info = agent.get_agent_info()
        print(f"🤖 Agent Role: {info['role']}")
        print(f"🧠 Model: {info['model']}")
        print(f"📅 Created: {info['created_at']}")
        
        # Test analysis (this will make an API call)
        print("\n🚀 Testing requirements analysis...")
        result = await agent.analyze_requirements(
            "Create a simple web application for managing personal tasks and to-do lists",
            context={'project_type': 'web_app', 'complexity': 'simple'}
        )
        
        print("✅ Analysis completed!")
        print(f"📋 Summary: {result.summary[:200]}...")
        print(f"🎯 Functional Requirements: {len(result.functional_requirements)} items")
        print(f"⚙️ Non-functional Requirements: {len(result.non_functional_requirements)} items")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing AnalystAgent: {e}")
        return False

async def test_orchestrator():
    """Test the BMadOrchestrator functionality."""
    print("\n🎯 Testing BMadOrchestrator...")
    
    try:
        from bmad_agents.agents.orchestrator import BMadOrchestrator
        
        # Create orchestrator
        orchestrator = BMadOrchestrator()
        print("✅ BMadOrchestrator created successfully!")
        
        # Test request routing
        print("\n🔀 Testing request routing...")
        response = await orchestrator.route_request(
            "I need help analyzing the requirements for a new mobile app"
        )
        
        print("✅ Request routed successfully!")
        print(f"🎯 Action: {response.action}")
        print(f"🤖 Target Agent: {response.target_agent}")
        print(f"💬 Message: {response.message[:150]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing BMadOrchestrator: {e}")
        return False

async def main():
    """Main test function."""
    print("🎯 BMad Agents - Simple Test")
    print("=" * 40)
    
    # Test individual components
    analyst_ok = await test_analyst_agent()
    orchestrator_ok = await test_orchestrator()
    
    print("\n" + "=" * 40)
    if analyst_ok and orchestrator_ok:
        print("✅ All tests passed! The BMad Agents system is working correctly.")
        print("\n🚀 Ready to use! Try running:")
        print("   $env:PYTHONPATH = '.'; python bmad_agents/examples/complete_example.py")
    else:
        print("⚠️  Some tests failed. Check your configuration:")
        print("   1. Ensure Google AI API key is set in .env")
        print("   2. Check internet connection")
        print("   3. Verify all dependencies are installed")

if __name__ == "__main__":
    asyncio.run(main())