{"workflow_id": "06adaddb-38a3-47b9-b8f4-69d55f089ea7", "workflow_type": "brownfield-fullstack", "current_step": "scope_classification", "status": "active", "progress": 0.0, "started_at": "2025-08-03 16:16:29.077381", "updated_at": "2025-08-03 16:16:29.077381", "context": {"enhancement_description": "test enhancement", "project_context": {"test": "data"}}, "participants": [], "completed_steps": [], "pending_steps": ["documentation_check", "project_analysis", "prd_creation", "architecture_decisions", "story_creation"], "agent_contexts": {}, "shared_context": {"enhancement_description": "test enhancement", "project_context": {"test": "data"}}}